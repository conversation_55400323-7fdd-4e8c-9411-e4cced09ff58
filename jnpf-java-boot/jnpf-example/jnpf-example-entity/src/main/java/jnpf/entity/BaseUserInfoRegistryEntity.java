package jnpf.entity;

import jnpf.annotation.FaModalName;
import jnpf.annotation.SqlSearch;
import jnpf.base.entity.SuperBaseEntity;
import jnpf.base.entity.SuperExtendEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 人员信息更新登记表
 *
 * <AUTHOR>
 * @version V3.5.0
 * @date 2025-01-01
 */
@FaModalName("人员信息更新登记表")
@Data
@TableName("base_user_info_registry")
public class BaseUserInfoRegistryEntity extends SuperBaseEntity.SuperTBaseEntity<String> {



    @TableId(value = "f_id")
    private String id;
    /**
     * 身份证号
     */
    @TableField("identification_number")
    @SqlSearch
    private String identificationNumber;

    /**
     * 原始用户ID
     */
    @TableField("original_user_id")
    private String originalUserId;

    /**
     * 姓名
     */
    @TableField("real_name")
    @SqlSearch
    private String realName;

    /**
     * 年龄
     */
    @TableField("age")
    private Integer age;

    /**
     * 性别 1-男 2-女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 联系方式
     */
    @TableField("mobile_phone")
    private String mobilePhone;

    /**
     * 紧急联系人
     */
    @TableField("emergency_contacts")
    private String emergencyContacts;

    /**
     * 紧急联系人电话
     */
    @TableField("emergency_contacts_phone")
    private String emergencyContactsPhone;

    /**
     * 籍贯
     */
    @TableField("native_place")
    private String nativePlace;

    /**
     * 民族
     */
    @TableField("nation")
    private String nation;

    /**
     * 家庭住址
     */
    @TableField("home_address")
    private String homeAddress;

    /**
     * 政治面貌
     */
    @TableField("political_outlook")
    private String politicalOutlook;

    /**
     * 学历
     */
    @TableField("education")
    private String education;

    /**
     * 所学专业
     */
    @TableField("specialty")
    private String specialty;

    /**
     * 毕业学校
     */
    @TableField("graduation_school")
    private String graduationSchool;

    /**
     * 毕业时间
     */
    @TableField("graduation_time")
    private Date graduationTime;

    /**
     * 首次参加工作时间
     */
    @TableField("join_work_time")
    private Date joinWorkTime;

    /**
     * 本工种工作年限
     */
    @TableField("seniority")
    private Integer seniority;

    /**
     * 婚姻状况
     */
    @TableField("marital_status")
    private String maritalStatus;

    /**
     * 部门
     */
    @TableField("organize_name")
    @SqlSearch
    private String organizeName;

    /**
     * 岗位
     */
    @TableField("position_name")
    private String positionName;

    /**
     * 岗位序列
     */
    @TableField("position_sequence")
    private String positionSequence;

    /**
     * 人员类别
     */
    @TableField("category_personnel")
    private String categoryPersonnel;

    /**
     * 用工方式
     */
    @TableField("form_employment")
    private String formEmployment;

    /**
     * 来源单位
     */
    @TableField("source_unit")
    private String sourceUnit;

    /**
     * 进项目时间
     */
    @TableField("go_project_time")
    private Date goProjectTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态 0-待审核 1-已审核通过 2-已拒绝 3-已应用
     */
    @TableField("status")
    private Integer status;

    /**
     * 审核人ID
     */
    @TableField("reviewer_id")
    private String reviewerId;

    /**
     * 审核人姓名
     */
    @TableField("reviewer_name")
    private String reviewerName;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private Date reviewTime;

    /**
     * 审核意见
     */
    @TableField("review_comment")
    private String reviewComment;
}
