package jnpf.permission.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import jnpf.base.biz.BaseTreeBiz;
import jnpf.constant.PermissionConst;
import jnpf.exception.DataException;
import jnpf.model.FaImportResultVo;
import jnpf.permission.entity.OrganizeEntity;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.mapper.OrganizeMapper;
import jnpf.service.FileSaveBiz;
import jnpf.util.*;
import jnpf.utils.FaExcelUtils;
import jnpf.vo.tree.TreeNode;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrganizeBiz extends BaseTreeBiz<OrganizeMapper, OrganizeEntity> {
    @Autowired FileSaveBiz fileSaveBiz;
    @Autowired UserBiz userBiz;
    @Autowired CacheKeyUtil cacheKeyUtil;
    @Autowired RedisUtil redisUtil;

    private static final String DEPARTMENT_CATE = "department";
    private static final String TEAM_CATE = "team";

    public void syncOldData() {
        lambdaUpdate()
            .isNull(OrganizeEntity::getEnabledMark)
            .set(OrganizeEntity::getEnabledMark, 1)
            .update();

        lambdaUpdate()
            .isNull(OrganizeEntity::getSortCode)
            .set(OrganizeEntity::getSortCode, 0L)
            .update();

        List<OrganizeEntity> organizations = this.list()
            .stream()
            .filter(org -> org.getOrganizeIdTree() != null && !org.getOrganizeIdTree().endsWith(org.getId()))
            .collect(Collectors.toList());

        organizations.forEach(org -> {
            String newIdTree = treePathLine(org.getParentId())
                .stream()
                .map(OrganizeEntity::getId)
                .collect(Collectors.joining(","));
            org.setOrganizeIdTree(newIdTree+","+org.getId());
        });

        if (!organizations.isEmpty()) {
            saveOrUpdateBatch(organizations);
        }
        redisUtil.remove(cacheKeyUtil.getOrganizeInfoList());
    }
    public List<OrganizeEntity> importPreview(String fileId) {
        File file = fileSaveBiz.getByFileId(fileId);
        List<OrganizeEntity> list = FaExcelUtils.simpleRead(file, OrganizeEntity.class);
        // 补全合并单元格的部门名称
        if (CollUtil.isEmpty(list) || list.size() <= 2) return Collections.emptyList();
        list.subList(0, 2).clear();

        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public FaImportResultVo<OrganizeEntity> importData(List<OrganizeEntity> list) {
        int snum = 0, fnum = 0; //记录成功、失败数量

        List<OrganizeEntity> errList = new ArrayList<>();
        List<String> failReasons = new ArrayList<>();

        for (OrganizeEntity entity : list) {
            /* 设置字段值 */
            setFieldVal(entity);

            try {
                List<OrganizeEntity> queryList = lambdaQuery()
                    .eq(OrganizeEntity::getParentId, entity.getParentId())
                    .eq(OrganizeEntity::getFullName, entity.getFullName())
                    .list();
                Long exitCount = Long.valueOf(queryList.size());

                if(exitCount > 1) throw new DataException("同级组织下，有多个重名组织："+entity.getFullName());

                entity.setEnabledMark(1);
                if (exitCount == 1) {
                    OrganizeEntity entityDB = queryList.get(0);

                    CopyOptions options = CopyOptions.create()
                        .setIgnoreNullValue(true)
                        .setOverride(true)
                        .setIgnoreProperties("id");
                    BeanUtil.copyProperties(entity, entityDB, options);

                    /*2.设置id_tree */
                    String organizeIdTree = super.treePathLine(entity.getParentId())
                        .stream()
                        .map(OrganizeEntity::getId)
                        .collect(Collectors.joining(","));
                    entityDB.setOrganizeIdTree(organizeIdTree + "," + entityDB.getId());

                    entityDB.setLastModifyUserId(UserProvider.getLoginUserId());
                    entityDB.setLastModifyTime(new Date());
                    this.updateById(entityDB);
                } else {
                    /*2.设置id_tree */
                    String organizeIdTree = super.treePathLine(entity.getParentId())
                        .stream()
                        .map(OrganizeEntity::getId)
                        .collect(Collectors.joining(","));
                    entity.setId(UUID.randomUUID().toString());
                    entity.setOrganizeIdTree(organizeIdTree + "," + entity.getId());

                    entity.setCreatorUserId(UserProvider.getLoginUserId());
                    entity.setCreatorTime(new Date());
                    this.save(entity);
                }
                snum++;
            } catch (Exception e) {
                errList.add(entity);
                failReasons.add(e.getMessage());
                fnum++;
//                log.error("导入第" + (fnum) + "条数据失败", e);
            }
        }

        redisUtil.remove(cacheKeyUtil.getOrganizeInfoList());

        FaImportResultVo<OrganizeEntity> vo = new FaImportResultVo<>();
        vo.setSnum(snum);
        vo.setFnum(fnum);
        if (vo.getFnum() > 0) {
            vo.setResultType(1);
            vo.setFailResult(errList);
            vo.setFailReasons(failReasons);
            return vo;
        } else {
            vo.setResultType(0);
            return vo;
        }
    }

    private void setFieldVal(OrganizeEntity entity){
        try {
            /*1.设置父级组织id */
//            String parentName = entity.getParentName();
//            OrganizeEntity parentOrg = lambdaQuery()
//                .eq(OrganizeEntity::getFullName, parentName)
//                .one();
            OrganizeEntity parentOrg = queryByCasPath(entity.getParentName());
            if (ObjUtil.isEmpty(parentOrg)) throw new DataException("所属组织异常，请仔细核对");
            entity.setParentId(parentOrg.getId());

        }catch (Exception e){
            throw new DataException("设置父级组织异常: "+e.getMessage());
        }

        /*3设置类型 */
        switch (entity.getCategory()) {
            case "部门":
                entity.setCategory(DEPARTMENT_CATE);
                break;
            case "班组":
                entity.setCategory(TEAM_CATE);
                break;
        }


        /*4设置相关人员id */
        try {
            for (String s : Arrays.asList("manager",
                "teamLeader",
                "mainManager")) {
                String fieldValue = "";
                //todo 处理空指针异常
                try {
                    fieldValue = ReflectUtil.getFieldValue(entity, s + ("Name")).toString();
                }catch (NullPointerException e){
                    continue;
                }

                UserEntity user = userBiz.lambdaQuery()
                    .eq(UserEntity::getRealName, fieldValue)
                    .one();
                ReflectUtil.setFieldValue(entity, s + ("Id"), user.getId());
            }
        }catch (Exception e){
            System.out.println("设置相关人员id异常: "+e);
            throw new DataException("设置相关人员id异常: "+e.getMessage());
        }

        /*5 设置编号*/
        if (ObjUtil.isNotEmpty(entity.getSortCode())) entity.setSortCode(0L);
    }


    @Override
    public String getRootId() {
        return "-1";
    }

    /**
     * 递归调用获取完整的组织名称，用/拼接
     *
     * @param id 组织ID
     * @return
     */
    public String getFullOrganizeName(String id) {
        OrganizeEntity organizeEntity = getByIdWithCache(id);
        if (organizeEntity == null) return "";
        String fullName = organizeEntity.getFullName();
        String parentId = organizeEntity.getParentId();
        if (StringUtil.isNotEmpty(parentId) && !parentId.equals("-1")) {
            // 递归调用，获取父组织的完整名称，并将其添加到当前名称的前面
            return getFullOrganizeName(parentId) + "/" + fullName;
        } else {
            // 基本情况：如果没有父组织，返回当前组织的名称
            return fullName;
        }
    }


    /***
     * 获取该组织链路上部门组织
     *
     * @param
     */
    public OrganizeEntity getDepartOrgan(String organizeId) {
        List<OrganizeEntity> organizeList = super.treePathLine(organizeId);
        if (CollectionUtil.isEmpty(organizeList)) {
            return null;
        }

        for (int i = organizeList.size() - 1; i >= 0; i--) {
            OrganizeEntity organizeEntity = organizeList.get(i);
            if (ObjUtil.equals(organizeEntity.getCategory(), PermissionConst.DEPARTMENT)) {
                return organizeEntity;
            }
        }
        return organizeList.get(organizeList.size() - 1);
    }

    /***
     * 获取该组织链路上班组组织
     *
     * @param
     */
    public OrganizeEntity getTeamOrgan(String organizeId) {
        List<OrganizeEntity> organizeList = super.treePathLine(organizeId);
        if (CollectionUtil.isEmpty(organizeList)) {
            return null;
        }

        for (int i = organizeList.size() - 1; i >= 0; i--) {
            OrganizeEntity organizeEntity = organizeList.get(i);
            if (ObjUtil.equals(organizeEntity.getCategory(), PermissionConst.TEAM)) {
                return organizeEntity;
            }
        }
        return organizeList.get(organizeList.size() - 1);
    }


    public OrganizeEntity getByName(String name) {
        LambdaQueryChainWrapper<OrganizeEntity> wrapper = lambdaQuery().eq(OrganizeEntity::getFullName, name);
        long count = wrapper.count();
        if (count > 1) {
            throw new DataException("找到多个同名的部门，部门名称不可重复：" + name);
        }
        return wrapper.one();
    }

    public OrganizeEntity getByNameWithCache(String name) {
        Map<Serializable, OrganizeEntity> cache = BaseContextHandler.getCacheMap("OrganizeBiz.getByNameWithCache");
        if (cache.containsKey(name)) {
            return cache.get(name);
        }
        OrganizeEntity entity = getByName(name);
        cache.put(name, entity);
        return entity;
    }

    @SneakyThrows
    public void setName(Object o, String fieldName) {
        String id = ReflectUtil.getFieldValue(o, fieldName).toString();
        OrganizeEntity org = this.getByIdWithCache(id);
        String name = (org != null ? org.getFullName() : "");
        ReflectUtil.setFieldValue(o, fieldName + "Name", name);
    }

    @SneakyThrows
    public void setIdByName(Object o, String fieldName, Class clazz) {
        String fullName = ReflectUtil.getFieldValue(o, fieldName + "Name").toString();
        List<OrganizeEntity> areaList = this.lambdaQuery()
            .eq(OrganizeEntity::getFullName, fullName)
            .list();

        Field field = ReflectUtil.getField(clazz, fieldName);
        String fileName = field.getAnnotation(ExcelProperty.class) != null ? field.getAnnotation(ExcelProperty.class).value()[0] : "";
        if (areaList.size() > 1) throw new DataException("导入字段" + fileName + "时，组织表有重名数据");

        OrganizeEntity entity = areaList.get(0);
        String id = (entity != null ? entity.getId() : "");
        ReflectUtil.setFieldValue(o, fieldName, id);
    }

    public void updateBatchCategory(List<String> ids, String category) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        if (!Arrays.asList(PermissionConst.DEPARTMENT, PermissionConst.TEAM).contains(category)) {
            throw new DataException("机构分类只能选择：部门、班组");
        }
        lambdaUpdate()
            .in(OrganizeEntity::getId, ids)
            .set(OrganizeEntity::getCategory, category)
            .update();
    }

    public List<TreeNode<OrganizeEntity>> allTreeDept() {
        List<OrganizeEntity> beanList = lambdaQuery()
            .ne(OrganizeEntity::getCategory, PermissionConst.TEAM)
            .list();
        return this.listToTree(beanList, getRootId());
    }

    public List<TreeNode<OrganizeEntity>> allFilteredTreeDept() {
        String[] hiddenOrg = {"所属劳务公司", "访客", "离场人员", "中核咨询"};
        List<OrganizeEntity> beanList = lambdaQuery()
            .ne(OrganizeEntity::getCategory, PermissionConst.TEAM)
            .notIn(OrganizeEntity::getFullName, hiddenOrg)
            .list();

        return this.listToTree(beanList, getRootId());
    }


    public List<String> getChildrenOrgCode(String division,Boolean isCode){
        //1.所属事业部
        OrganizeEntity org = lambdaQuery()
            .eq(!isCode,OrganizeEntity::getFullName, division)
            .eq(isCode,OrganizeEntity::getCode,division)
            .orderByDesc(OrganizeEntity::getCreatorTime)
            .last("limit 1")
            .one();

        if (ObjUtil.isEmpty(org)) {
            return Collections.emptyList();
        }
        List<OrganizeEntity> organList = getAllChildrenFromNode(org.getId());

        if (CollectionUtil.isEmpty(organList)) {
            return Collections.emptyList();
        }
        List<String> organIds = organList.stream().map(i -> StrUtil.toString(i.getId())).collect(Collectors.toList());
        return organIds;
    }

    /**
     * 组织路径查询，支持如：xx项目部/xx部门
     * @param casPath
     * @return
     */
    public OrganizeEntity queryByCasPath(String casPath) throws DataException {
        if (StrUtil.isEmpty(casPath)) return null;

        List<OrganizeEntity> orgList = new ArrayList<>();
        OrganizeEntity orgParent = null;
        String[] paths = casPath.split("/");
        for (String path : paths) {
            LambdaQueryChainWrapper<OrganizeEntity> chainWrapper = lambdaQuery()
                .eq(OrganizeEntity::getFullName, path)
                .eq(orgParent != null, OrganizeEntity::getParentId, orgParent != null ? orgParent.getId() : "");
            long count = chainWrapper.count();
            if (count > 1) {
                throw new DataException("组织路径有重名数据：" + path);
            }
            if (count == 0) {
                return null;
            }
            orgParent = chainWrapper.one();
            orgList.add(orgParent);
        }
        return orgParent;
    }

    /**
     * 通过部门名称，获取部门ID。部门名称组织路径查询，支持如：xx项目部/xx部门
     * @return
     */
    public String getOrgIdByOrgName(String orgName) {
        OrganizeEntity organize = this.queryByCasPath(orgName);
        return organize != null ? organize.getId() : null;
    }

    public String getOrgIdByOrgNameWithCache(String orgName) {
        Map<Serializable, String> cache = BaseContextHandler.getCacheMap("OrganizeBiz.getOrgIdByOrgNameWithCache");
        if (cache.containsKey(orgName)) {
            return cache.get(orgName);
        }
        String entity = getOrgIdByOrgName(orgName);
        cache.put(orgName, entity);
        return entity;
    }

}
