<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jnpf-common</artifactId>
        <groupId>com.jnpf</groupId>
        <version>3.5.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jnpf-cloud-common</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>jnpf-common-dubbo</module>
        <module>jnpf-common-feign</module>
        <module>jnpf-common-seata</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <maven-compiler-plugin.version>3.8.0</maven-compiler-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--<dependency>
                <groupId>com.jnpf</groupId>
                <artifactId>jnpf-boot-common</artifactId>
                <type>pom</type>
                <scope>import</scope>
            </dependency>-->
        </dependencies>
    </dependencyManagement>

</project>