package jnpf.utils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright xx有限公司
 * @date 2021/3/16 10:51
 */
@Data
public class FeignName {
    public static final String SYSTEM_SERVER_NAME = "jnpf-system" ;

    public static final String MESSAGE_SERVER_NAME = "jnpf-message" ;

    public static final String VUSUALDEV_SERVER_NAME = "jnpf-visualdev" ;

    public static final String EXAMPLE_SERVER_NAME = "jnpf-example" ;

    public static final String EXTEND_SERVER_NAME = "jnpf-extend" ;

    public static final String APP_SERVER_NAME = "jnpf-app" ;

    public static final String WORKFLOW_SERVER_NAME = "jnpf-workflow" ;

    public static final String FILE_SERVER_NAME = "jnpf-file";

    public static final String PERMISSION_SERVER_NAME = "jnpf-permission";

    public static final String FORM_SERVER_NAME = "jnpf-flowForm";

    public static final String OAUTH_SERVER_NAME = "jnpf-oauth";

    public static final String SCHEDULE_SERVER_NAME = "jnpf-scheduletask" ;

}
