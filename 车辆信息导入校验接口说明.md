# 车辆信息导入校验接口说明

## 新增接口概述
在原有的导入预览功能基础上，新增了 `validateImportData` 接口，用于前端编辑数据后的重新校验功能。

## 接口详情

### 1. Controller 接口
**文件路径**: `jnpf-java-boot/jnpf-vehicle/src/main/java/jnpf/vehicle/controller/VehicleInfoController.java`

```java
/**
 * 校验导入数据
 */
@Operation(summary = "校验导入数据")
@PostMapping("/validateImportData")
public ActionResult<List<VehicleInfoEntityErrorVO>> validateImportData(@RequestBody FaImportReqVo<VehicleInfoEntity> params) {
    List<VehicleInfoEntity> dataList = params.getList();
    return baseBiz.validateImportData(dataList);
}
```

**接口信息**:
- **请求方式**: POST
- **请求路径**: `/api/base/vehicle/info/validateImportData`
- **请求参数**: `FaImportReqVo<VehicleInfoEntity>` - 包装的车辆信息列表
- **返回结果**: `ActionResult<List<VehicleInfoEntityErrorVO>>` - 包含校验错误信息的结果列表

### 2. Service 业务逻辑
**文件路径**: `jnpf-java-boot/jnpf-vehicle/src/main/java/jnpf/vehicle/service/VehicleInfoBiz.java`

```java
/**
 * 校验导入数据
 *
 * @param dataList 前端编辑后的数据列表
 * @return 校验结果列表（包含错误信息）
 */
public ActionResult<List<VehicleInfoEntityErrorVO>> validateImportData(List<VehicleInfoEntity> dataList) {
    // 转换为错误VO列表并收集异常信息
    List<VehicleInfoEntityErrorVO> errorList = new ArrayList<>();
    
    for (VehicleInfoEntity vo : dataList) {
        VehicleInfoEntityErrorVO errorVo = new VehicleInfoEntityErrorVO();
        BeanUtils.copyProperties(vo, errorVo);
        
        // 收集异常信息
        List<String> errors = new ArrayList<>();
        
        // 各种数据校验...
        
        // 设置错误信息
        if (!errors.isEmpty()) {
            errorVo.setErrorsInfo(String.join("；", errors));
        }
        
        errorList.add(errorVo);
    }
    
    return ActionResult.success(errorList);
}
```

## 校验规则

### 数据校验项目
1. **车牌号校验**
   - 不能为空
   - 不能与数据库中已有数据重复（排除当前记录ID）

2. **车辆状态校验**
   - 不能为空
   - 必须是有效的枚举值（可用、维修中、报废）

3. **车辆型号校验**
   - 不能为空

4. **车辆使用单位校验**
   - 不能为空

5. **吨位校验**
   - 如果有值，必须大于0

### 错误信息处理
- 多个错误信息用分号（；）分隔
- 错误信息存储在 `errorsInfo` 字段中
- 前端可以根据该字段判断数据是否有效并显示具体错误原因

## 使用流程

### 1. 导入预览流程
```
用户上传Excel文件 
    ↓
调用 /ImportPreview 接口
    ↓
返回预览数据（包含初始校验错误）
    ↓
前端显示数据和错误信息
```

### 2. 数据编辑和重新校验流程
```
用户在前端编辑数据
    ↓
调用 /validateImportData 接口
    ↓
后端重新校验编辑后的数据
    ↓
返回最新的校验结果
    ↓
前端更新显示校验结果
```

### 3. 最终导入流程
```
用户确认数据无误
    ↓
调用 /importData 接口
    ↓
执行实际的数据导入操作
```

## 请求示例

### 请求参数
```json
{
  "list": [
    {
      "id": null,
      "vehicleNumber": "京A12345",
      "vehicleModel": "奔驰GLC",
      "vehicleUseUnit": "测试单位",
      "tonnage": 2.5,
      "status": "AVAILABLE",
      "remark": "测试车辆"
    },
    {
      "id": null,
      "vehicleNumber": "京B67890",
      "vehicleModel": "",
      "vehicleUseUnit": "测试单位2",
      "tonnage": -1.0,
      "status": null,
      "remark": "有问题的车辆"
    }
  ]
}
```

### 响应结果
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": null,
      "vehicleNumber": "京A12345",
      "vehicleModel": "奔驰GLC",
      "vehicleUseUnit": "测试单位",
      "tonnage": 2.5,
      "status": "AVAILABLE",
      "remark": "测试车辆",
      "statusName": "可用",
      "errorsInfo": null
    },
    {
      "id": null,
      "vehicleNumber": "京B67890",
      "vehicleModel": "",
      "vehicleUseUnit": "测试单位2",
      "tonnage": -1.0,
      "status": null,
      "remark": "有问题的车辆",
      "statusName": null,
      "errorsInfo": "车辆型号不能为空；车辆状态不能为空；吨位必须大于0"
    }
  ]
}
```

## 与导入预览接口的区别

| 特性 | 导入预览接口 | 校验接口 |
|------|-------------|----------|
| **数据来源** | Excel文件 | 前端编辑后的数据 |
| **请求方式** | GET | POST |
| **参数类型** | fileId | List<VehicleInfoEntity> |
| **主要用途** | 首次预览Excel数据 | 重新校验编辑后的数据 |
| **额外处理** | 包含Excel解析和状态转换 | 纯数据校验 |

## 优势特点

1. **实时校验**: 用户编辑数据后可以立即重新校验
2. **灵活性**: 支持部分数据修改后的重新校验
3. **一致性**: 使用相同的校验规则和错误格式
4. **用户体验**: 避免用户修改数据后还需要重新上传文件
5. **数据完整性**: 确保最终导入的数据都经过完整校验

这个接口完善了整个导入流程，让用户可以在导入前充分编辑和校验数据，提高了数据导入的准确性和用户体验。

## 问题修复记录

### JSON反序列化错误修复
**问题**: 接口调用时出现 `HttpMessageNotReadableException` 错误，无法将JSON对象反序列化为ArrayList。

**原因**:
- 后端接口参数定义为 `List<VehicleInfoEntity>`
- 前端发送的数据格式为 `{list: [...]}`，是一个包含list属性的对象
- Jackson无法将对象直接转换为数组类型

**解决方案**:
1. 修改接口参数类型为 `FaImportReqVo<VehicleInfoEntity>`
2. 在方法内部通过 `params.getList()` 获取实际的数据列表
3. 保持与系统其他导入接口的一致性

**修改前**:
```java
public ActionResult<List<VehicleInfoEntityErrorVO>> validateImportData(@RequestBody List<VehicleInfoEntity> dataList)
```

**修改后**:
```java
public ActionResult<List<VehicleInfoEntityErrorVO>> validateImportData(@RequestBody FaImportReqVo<VehicleInfoEntity> params) {
    List<VehicleInfoEntity> dataList = params.getList();
    // ...
}
```

这样修改后，接口能够正确接收前端发送的包装格式数据，解决了JSON反序列化问题。
