/**
 * BaseUserInfoRegistryApi 使用示例
 * 展示如何调用 saveOrUpdate 方法并设置 status 为待审核状态（0）
 */

import { baseUserInfoRegistryApi, type BaseUserInfoRegistryEntity } from '/@/api';

/**
 * 保存或更新人员信息登记表示例
 */
export async function saveUserInfoRegistryExample() {
  try {
    // 准备要保存的数据
    const userInfoData: Partial<BaseUserInfoRegistryEntity> = {
      // 基本信息
      identificationNumber: '110101199001011234', // 身份证号
      originalUserId: 'user123', // 原始用户ID
      realName: '张三', // 姓名
      age: 30, // 年龄
      gender: 1, // 性别 1-男 2-女
      mobilePhone: '13800138000', // 联系方式
      
      // 紧急联系人信息
      emergencyContacts: '李四', // 紧急联系人
      emergencyContactsPhone: '13900139000', // 紧急联系人电话
      
      // 个人详细信息
      nativePlace: '北京市', // 籍贯
      nation: '汉族', // 民族
      homeAddress: '北京市朝阳区某某街道', // 家庭住址
      politicalOutlook: '群众', // 政治面貌
      
      // 教育信息
      education: '大学本科', // 学历
      specialty: '计算机科学与技术', // 所学专业
      graduationSchool: '北京大学', // 毕业学校
      graduationTime: '2015-06-30', // 毕业时间
      
      // 工作信息
      joinWorkTime: '2015-07-01', // 首次参加工作时间
      seniority: 8, // 本工种工作年限
      maritalStatus: '已婚', // 婚姻状况
      
      // 组织信息
      organizeName: '技术部', // 部门
      positionName: '软件工程师', // 职位
      
      // 其他信息
      formEmployment: '正式员工', // 用工形式
      sourceUnit: '社会招聘', // 来源单位
      categoryPersonnel: '技术人员', // 人员类别
      goProjectTime: '2023-01-01', // 进项目时间
      remark: '技术骨干，工作认真负责', // 备注
      
      // 重要：设置状态为待审核
      status: 0, // 0-待审核 1-已审核通过 2-已拒绝 3-已应用
    };

    // 调用 saveOrUpdate 方法
    const result = await baseUserInfoRegistryApi.saveOrUpdate(userInfoData);
    
    console.log('保存成功:', result);
    return result;
    
  } catch (error) {
    console.error('保存失败:', error);
    throw error;
  }
}

/**
 * 更新现有记录示例
 */
export async function updateUserInfoRegistryExample(id: string) {
  try {
    // 准备更新数据（包含ID表示这是更新操作）
    const updateData: Partial<BaseUserInfoRegistryEntity> = {
      id: id, // 有ID表示更新操作
      realName: '张三（更新）',
      age: 31,
      mobilePhone: '13800138001',
      homeAddress: '北京市朝阳区新地址',
      remark: '信息已更新',
      status: 0, // 重新设置为待审核状态
    };

    // 调用 saveOrUpdate 方法
    const result = await baseUserInfoRegistryApi.saveOrUpdate(updateData);
    
    console.log('更新成功:', result);
    return result;
    
  } catch (error) {
    console.error('更新失败:', error);
    throw error;
  }
}

/**
 * 在Vue组件中使用的示例
 */
export const useUserInfoRegistry = () => {
  const saveUserInfo = async (formData: any) => {
    try {
      // 准备提交数据
      const submitData = {
        ...formData,
        status: 0, // 设置为待审核状态
        // 处理时间字段
        graduationTime: formData.graduationTime ? 
          (formData.graduationTime instanceof Date ? 
            formData.graduationTime.toISOString() : 
            formData.graduationTime) : null,
        joinWorkTime: formData.joinWorkTime ? 
          (formData.joinWorkTime instanceof Date ? 
            formData.joinWorkTime.toISOString() : 
            formData.joinWorkTime) : null,
      };

      // 调用API
      const result = await baseUserInfoRegistryApi.saveOrUpdate(submitData);
      
      return {
        success: true,
        data: result,
        message: '信息提交成功，等待审核'
      };
      
    } catch (error) {
      return {
        success: false,
        error: error,
        message: '提交失败，请稍后重试'
      };
    }
  };

  return {
    saveUserInfo
  };
};

/**
 * 状态常量定义
 */
export const USER_INFO_STATUS = {
  PENDING: 0,    // 待审核
  APPROVED: 1,   // 已审核通过
  REJECTED: 2,   // 已拒绝
  APPLIED: 3,    // 已应用
} as const;

/**
 * 获取状态文本
 */
export function getStatusText(status: number): string {
  const statusMap = {
    [USER_INFO_STATUS.PENDING]: '待审核',
    [USER_INFO_STATUS.APPROVED]: '已审核通过',
    [USER_INFO_STATUS.REJECTED]: '已拒绝',
    [USER_INFO_STATUS.APPLIED]: '已应用',
  };
  
  return statusMap[status] || '未知状态';
}
