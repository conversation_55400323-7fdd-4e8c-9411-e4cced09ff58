<template>
  <div class="user-info-update">
    <div class="container">
      <div class="header">
        <h1 class="title">人员信息更新</h1>
        <p class="subtitle">请输入身份证号查询人员信息</p>
      </div>

      <div class="content">
        <!-- 身份证输入阶段 -->
        <div v-if="currentStep === 'search'" class="search-section">
          <div class="search-form">
            <a-input
              v-model:value="searchForm.identificationNumber"
              placeholder="请输入身份证号"
              size="large"
              :maxlength="18"
              @pressEnter="handleSearch"
              class="id-input" />
            <a-button type="primary" size="large" @click="handleSearch" :loading="searching" class="search-btn"> 查询 </a-button>
          </div>
          <div v-if="searchError" class="error-message">
            {{ searchError }}
          </div>
        </div>

        <!-- 未找到人员信息 -->
        <div v-if="currentStep === 'not-found'" class="not-found-section">
          <div class="not-found-icon">❌</div>
          <h3>未搜索到人员信息</h3>
          <p>身份证号：{{ searchForm.identificationNumber }}</p>
          <p>请检查身份证号是否正确，或联系管理员</p>
          <a-button type="primary" @click="backToSearch">重新查询</a-button>
        </div>

        <!-- 人员信息编辑表单 -->
        <div v-if="currentStep === 'edit'" class="edit-section">
          <div class="user-basic-info">
            <h3>{{ userInfo.realName || '未知' }} 的信息</h3>
            <p class="id-number">身份证：{{ userInfo.identificationNumber }}</p>
          </div>

          <a-form :model="formData" :rules="formRules" ref="formRef" layout="vertical" class="user-form">
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item label="姓名" name="realName">
                  <a-input v-model:value="formData.realName" placeholder="请输入姓名" />
                </a-form-item>
              </a-col>

              <a-col :span="12">
                <a-form-item label="年龄" name="age">
                  <a-input-number v-model:value="formData.age" placeholder="请输入年龄" :min="1" :max="100" style="width: 100%" />
                </a-form-item>
              </a-col>

              <a-col :span="12">
                <a-form-item label="性别" name="gender">
                  <a-select v-model:value="formData.gender" placeholder="请选择性别">
                    <a-select-option :value="1">男</a-select-option>
                    <a-select-option :value="2">女</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="联系方式" name="mobilePhone">
                  <a-input v-model:value="formData.mobilePhone" placeholder="请输入手机号" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="紧急联系人" name="emergencyContacts">
                  <a-input v-model:value="formData.emergencyContacts" placeholder="请输入紧急联系人" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="紧急联系人电话" name="emergencyContactsPhone">
                  <a-input v-model:value="formData.emergencyContactsPhone" placeholder="请输入紧急联系人电话" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="籍贯" name="nativePlace">
                  <a-input v-model:value="formData.nativePlace" placeholder="请输入籍贯" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="民族" name="nation">
                  <jnpf-select v-model:value="formData.nation" placeholder="请选择民族" :options="nationOptions" show-search />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="家庭住址" name="homeAddress">
                  <a-input v-model:value="formData.homeAddress" placeholder="请输入地址（精确到村/小区门牌号）" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="政治面貌" name="politicalOutlook">
                  <a-select v-model:value="formData.politicalOutlook" placeholder="请选择政治面貌">
                    <a-select-option value="群众">群众</a-select-option>
                    <a-select-option value="共青团员">共青团员</a-select-option>
                    <a-select-option value="积极分子">积极分子</a-select-option>
                    <a-select-option value="中共预备党员">中共预备党员</a-select-option>
                    <a-select-option value="中共党员">中共党员</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="学历" name="education">
                  <jnpf-select v-model:value="formData.education" :options="educationOptions" placeholder="请选择学历" show-search />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="所学专业" name="specialty">
                  <a-input v-model:value="formData.specialty" placeholder="小学、初中、高中学历人员填'无'" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="毕业学校" name="graduationSchool">
                  <a-input v-model:value="formData.graduationSchool" placeholder="请输入毕业学校" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="毕业时间" name="graduationTime">
                  <jnpf-date-picker v-model:value="formData.graduationTime" format="YYYY-MM-DD" allowClear />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="首次参加工作时间" name="joinWorkTime">
                  <jnpf-date-picker v-model:value="formData.joinWorkTime" format="YYYY-MM-DD" allowClear />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="本工种工作年限" name="seniority">
                  <a-input-number v-model:value="formData.seniority" placeholder="如之前未进入核电工作，则填写0" :min="0" :max="50" style="width: 100%" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="婚姻状况" name="maritalStatus">
                  <a-select v-model:value="formData.maritalStatus" placeholder="请选择婚姻状况">
                    <a-select-option value="未婚">未婚</a-select-option>
                    <a-select-option value="已婚">已婚</a-select-option>
                    <a-select-option value="离异">离异</a-select-option>
                    <a-select-option value="丧偶">丧偶</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="部门" name="organizeName">
                  <a-input v-model:value="formData.organizeName" placeholder="部门" readonly />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="岗位" name="positionName">
                  <a-input v-model:value="formData.positionName" placeholder="岗位" readonly />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="岗位序列" name="positionSequence">
                  <jnpf-select v-model:value="formData.positionSequence" :options="positionSequenceOptions" placeholder="请选择岗位序列" show-search />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="人员类别" name="categoryPersonnel">
                  <a-input v-model:value="formData.categoryPersonnel" placeholder="请输入人员类别" />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="用工方式" name="formEmployment">
                  <a-input v-model:value="formData.formEmployment" placeholder="用工方式" readonly />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="来源单位" name="sourceUnit">
                  <a-input v-model:value="formData.sourceUnit" placeholder="来源单位" readonly />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="进项目时间" name="goProjectTime">
                  <jnpf-date-picker v-model:value="formData.goProjectTime" format="YYYY-MM-DD" allowClear readonly />
                </a-form-item>
              </a-col>

              <a-col :span="24">
                <a-form-item label="备注" name="remark">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="3" />
                </a-form-item>
              </a-col>
            </a-row>

            <div class="form-actions">
              <a-button @click="backToSearch" style="margin-right: 16px">重新查询</a-button>
              <a-button type="primary" @click="handleSubmit" :loading="submitting"> 保存更新</a-button>
            </div>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useBaseStore } from '/@/store/modules/base';
  import JnpfDatePicker from '/@/components/Jnpf/DatePicker/src/DatePicker.vue';
  import JnpfSelect from '/@/components/Jnpf/Select/src/Select.vue';
  import baseUserInfoRegistryApi from "/@/api/example/BaseUserInfoRegistryApi";

  defineOptions({ name: 'UserInfoUpdate' });

  const baseStore = useBaseStore();
  // 当前步骤：search(搜索) | not-found(未找到) | edit(编辑)
  const currentStep = ref('search');
  const searching = ref(false);
  const submitting = ref(false);
  const searchError = ref('');
  const formRef = ref();

  // 搜索表单
  const searchForm = reactive({
    identificationNumber: '',
  });

  // 用户信息
  const userInfo = ref<any>({});

  // 编辑表单数据
  const formData = reactive({
    id: '',
    realName: '',
    age: null,
    gender: '',
    mobilePhone: '',
    emergencyContacts: '',
    emergencyContactsPhone: '',
    nativePlace: '',
    nation: '',
    homeAddress: '',
    positionSequence: '',
    politicalOutlook: '',
    education: '',
    specialty: '',
    graduationSchool: '',
    graduationTime: null,
    joinWorkTime: null,
    seniority: null,
    maritalStatus: '',
    organizeName: '',
    positionName: '',
    formEmployment: '',
    sourceUnit: '',
    categoryPersonnel: '',
    goProjectTime: null,
    remark: '',
  });

  const educationOptions = ref([]);
  const nationOptions = ref([]);
  const positionSequenceOptions = ref([]);

  // 自定义毕业时间校验
  const validateGraduationTime = async (_rule, value) => {
    if (!value) return Promise.resolve();

    const idNumber = userInfo.value?.identificationNumber;
    const educationId = formData.education;

    if (!idNumber || idNumber.length !== 18 || !educationId) {
      return Promise.resolve();
    }

    const birthYear = parseInt(idNumber.substring(6, 10), 10);
    const selectedYear = new Date(value).getFullYear();

    const educationItem = educationOptions.value.find(item => item.id === educationId);
    const educationName = educationItem ? educationItem.fullName : '';

    const educationAges = {
      小学: 12,
      初中: 15,
      '中专/中技': 18,
      高中: 18,
      大学本科: 22,
      硕士: 25,
    };

    const normalGraduationAge = educationAges[educationName] || 18;
    const earliestReasonableYear = birthYear + normalGraduationAge - 4;

    if (selectedYear < earliestReasonableYear) {
      return Promise.reject(`根据您的年龄和学历，最早合理毕业年份为${earliestReasonableYear}年`);
    }

    return Promise.resolve();
  };

  // 表单验证规则
  const formRules = {
    realName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    age: [{ required: true, message: '请输入年龄', trigger: 'change' }],
    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
    mobilePhone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    ],
    homeAddress: [{ required: true, message: '请输入地址', trigger: 'blur' }],
    politicalOutlook: [{ required: true, message: '请选择政治面貌', trigger: 'change' }],
    graduationSchool: [{ required: true, message: '请输入毕业学校', trigger: 'blur' }],
    graduationTime: [
      { required: true, message: '请选择毕业时间', trigger: 'change' },
      { validator: validateGraduationTime, trigger: 'change' },
    ],
    joinWorkTime: [{ required: true, message: '请选择首次参加工作时间', trigger: 'change' }],
    seniority: [{ required: true, message: '请输入从事本工种工作年限', trigger: 'change' }],
    maritalStatus: [{ required: true, message: '请选择婚姻状况', trigger: 'change' }],
    emergencyContacts: [{ required: true, message: '请输入紧急联系人', trigger: 'blur' }],
    emergencyContactsPhone: [
      { required: true, message: '请输入紧急联系人电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    ],
    nativePlace: [{ required: true, message: '请输入籍贯', trigger: 'blur' }],
    nation: [{ required: true, message: '请选择民族', trigger: 'change' }],
    education: [{ required: true, message: '请选择学历', trigger: 'change' }],
    specialty: [{ required: true, message: '请输入所学专业', trigger: 'blur' }],
    categoryPersonnel: [{ required: true, message: '请输入人员类别', trigger: 'blur' }],
  };

  // 搜索人员信息
  const handleSearch = async () => {
    if (!searchForm.identificationNumber.trim()) {
      searchError.value = '请输入身份证号';
      return;
    }

    // 简单的身份证号格式验证
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(searchForm.identificationNumber)) {
      searchError.value = '请输入正确的身份证号格式';
      return;
    }

    searchError.value = '';
    searching.value = true;

    try {
      // 调用人员搜索接口
      const response = await defHttp.post({
        url: '/api/example/RemoveUser/getMasterListWithNames',
        data: {
          currentPage: 1,
          pageSize: 10,
          identificationNumber: searchForm.identificationNumber,
        },
      });

      if (response && response.data && response.data.list && response.data.list.length > 0) {
        // 找到人员信息
        const user = response.data.list[0];
        userInfo.value = user;

        // 填充表单数据
        Object.keys(formData).forEach(key => {
          if (key === 'graduationTime' || key === 'joinWorkTime' || key === 'goProjectTime') {
            // 将时间戳转换为 Date 对象
            if (user[key]) {
              formData[key] = new Date(user[key]);
            } else {
              formData[key] = null;
            }
          } else {
            formData[key] = user[key];
          }
        });

        currentStep.value = 'edit';
      } else {
        // 未找到人员信息
        currentStep.value = 'not-found';
      }
    } catch (error) {
      console.error('搜索人员信息失败:', error);
      searchError.value = '查询失败，请稍后重试';
    } finally {
      searching.value = false;
    }
  };

  // 返回搜索页面
  const backToSearch = () => {
    currentStep.value = 'search';
    searchForm.identificationNumber = '';
    searchError.value = '';
    // 重置表单数据
    Object.keys(formData).forEach(key => {
      if (key === 'age' || key === 'seniority') {
        formData[key] = null;
      } else if (key === 'graduationTime' || key === 'joinWorkTime' || key === 'goProjectTime') {
        formData[key] = null;
      } else if (key === 'specialty') {
        formData[key] = '无';
      } else if (key === 'education') {
        formData[key] = '';
      } else {
        formData[key] = '';
      }
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formRef.value.validate();
      submitting.value = true;

      // 准备提交数据
      const submitData = {
        ...formData,
        identificationNumber: searchForm.identificationNumber, // 身份证号
        originalUserId: userInfo.value.id, // 原始用户ID
        status: 0, // 待审核状态
        // 确保时间字段格式正确
        graduationTime: formData.graduationTime ? formData.graduationTime.toISOString() : null,
        joinWorkTime: formData.joinWorkTime ? formData.joinWorkTime.toISOString() : null,
        goProjectTime: formData.goProjectTime ? formData.goProjectTime.toISOString() : null,
      };

      // 调用BaseUserInfoRegistryApi的saveOrUpdate方法
      await baseUserInfoRegistryApi.saveOrUpdate(submitData);

      message.success('信息更新申请已提交，等待审核！');

      // 可以选择返回搜索页面或保持在当前页面
      setTimeout(() => {
        backToSearch();
      }, 2000);
    } catch (error) {
      if (error.errorFields) {
        message.error('请检查表单信息');
      } else {
        console.error('提交失败:', error);
        message.error('提交失败，请稍后重试');
      }
    } finally {
      submitting.value = false;
    }
  };

  onMounted(async () => {
    educationOptions.value = ((await baseStore.getDictionaryData('Education')) as any[]) || [];
    nationOptions.value = ((await baseStore.getDictionaryData('Nation')) as any[]) || [];
    positionSequenceOptions.value = ((await baseStore.getDictionaryData('PositionSequence')) as any[]) || [];
  });
</script>

<style lang="less" scoped>
  .user-info-update {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 600px;
    width: 100%;
  }

  .header {
    text-align: center;
    margin-bottom: 40px;
  }

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
  }

  .search-section {
    text-align: center;
  }

  .search-form {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
  }

  .id-input {
    flex: 1;
  }

  .search-btn {
    min-width: 80px;
  }

  .error-message {
    color: #ff4d4f;
    font-size: 14px;
    text-align: center;
  }

  .not-found-section {
    text-align: center;
    padding: 40px 0;
  }

  .not-found-icon {
    font-size: 48px;
    margin-bottom: 20px;
  }

  .not-found-section h3 {
    color: #333;
    margin-bottom: 16px;
  }

  .not-found-section p {
    color: #666;
    margin-bottom: 8px;
  }

  .edit-section {
    .user-basic-info {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 24px;
      text-align: center;

      h3 {
        margin: 0 0 8px 0;
        color: #333;
      }

      .id-number {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .user-form {
      .form-actions {
        text-align: center;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .container {
      padding: 20px;
      margin: 10px;
    }

    .search-form {
      flex-direction: column;
    }

    .search-btn {
      width: 100%;
    }
  }
</style>
