# 车辆信息导入预览接口修改说明

## 修改概述
参考 `RemoveUserController` 的导入预览实现，为车辆信息管理模块的导入预览接口添加了错误信息字段，以便在导入预览时显示数据验证错误。

## 修改内容

### 1. 新增错误VO类
**文件路径**: `jnpf-java-boot/jnpf-vehicle/src/main/java/jnpf/vehicle/entity/VehicleInfoEntityErrorVO.java`

```java
@EqualsAndHashCode(callSuper = true)
@Data
public class VehicleInfoEntityErrorVO extends VehicleInfoEntity {
    /**
     * 异常原因
     */
    @Excel(name = "异常原因", orderNum = "999")
    @JSONField(name = "errorsInfo")
    private String errorsInfo;
}
```

**特点**:
- 继承自 `VehicleInfoEntity`，包含所有原有字段
- 新增 `errorsInfo` 字段用于存储验证错误信息
- 使用 `@Excel` 注解支持Excel导出，排序号为999确保在最后一列
- 使用 `@JSONField` 注解指定JSON序列化字段名

### 2. 修改Controller接口
**文件路径**: `jnpf-java-boot/jnpf-vehicle/src/main/java/jnpf/vehicle/controller/VehicleInfoController.java`

**修改前**:
```java
@GetMapping("/ImportPreview")
public ActionResult<List<VehicleInfoEntity>> importPreview(@RequestParam("fileId") String fileId) {
    List<VehicleInfoEntity> list = baseBiz.importPreview(fileId);
    return ActionResult.success(list);
}
```

**修改后**:
```java
@GetMapping("/ImportPreview")
public ActionResult<List<VehicleInfoEntityErrorVO>> importPreview(@RequestParam("fileId") String fileId) {
    return baseBiz.importPreview(fileId);
}
```

**变化**:
- 返回类型从 `List<VehicleInfoEntity>` 改为 `List<VehicleInfoEntityErrorVO>`
- 直接返回Service层的ActionResult，不再手动包装

### 3. 修改Service业务逻辑
**文件路径**: `jnpf-java-boot/jnpf-vehicle/src/main/java/jnpf/vehicle/service/VehicleInfoBiz.java`

**修改前**:
```java
public List<VehicleInfoEntity> importPreview(String fileId) {
    File file = fileSaveBiz.getByFileId(fileId);
    List<VehicleInfoEntity> list = FaExcelUtils.simpleRead(file, VehicleInfoEntity.class);
    
    // 简单的状态转换处理
    for (VehicleInfoEntity vehicle : list) {
        if (StrUtil.isNotBlank(vehicle.getStatusName())) {
            VehicleStatusEnum statusEnum = matchVehicleStatus(vehicle.getStatusName());
            vehicle.setStatus(statusEnum);
        }
    }
    
    return list;
}
```

**修改后**:
```java
public ActionResult<List<VehicleInfoEntityErrorVO>> importPreview(String fileId) {
    File file = fileSaveBiz.getByFileId(fileId);
    List<VehicleInfoEntity> list = FaExcelUtils.simpleRead(file, VehicleInfoEntity.class);
    
    // 转换为错误VO列表并收集异常信息
    List<VehicleInfoEntityErrorVO> errorList = new ArrayList<>();
    for (VehicleInfoEntity vo : list) {
        VehicleInfoEntityErrorVO errorVo = new VehicleInfoEntityErrorVO();
        BeanUtils.copyProperties(vo, errorVo);
        
        // 收集异常信息
        List<String> errors = new ArrayList<>();
        
        // 验证车牌号
        if (StrUtil.isEmpty(vo.getVehicleNumber())) {
            errors.add("车牌号不能为空");
        } else if (isVehicleNumberExists(vo.getVehicleNumber(), vo.getId())) {
            errors.add("车牌号已存在");
        }
        
        // 处理车辆状态验证
        if (StrUtil.isNotBlank(vo.getStatusName())) {
            VehicleStatusEnum statusEnum = matchVehicleStatus(vo.getStatusName());
            if (statusEnum == null) {
                errors.add("车辆状态不正确，应为：可用、维修中、报废");
            } else {
                errorVo.setStatus(statusEnum);
            }
        }
        
        // 验证其他必填字段
        if (StrUtil.isEmpty(vo.getVehicleModel())) {
            errors.add("车辆型号不能为空");
        }
        
        if (StrUtil.isEmpty(vo.getVehicleUseUnit())) {
            errors.add("车辆使用单位不能为空");
        }
        
        // 设置错误信息
        if (!errors.isEmpty()) {
            errorVo.setErrorsInfo(String.join("；", errors));
        }
        
        errorList.add(errorVo);
    }
    
    return ActionResult.success(errorList);
}
```

## 新增验证规则

### 数据验证项目
1. **车牌号验证**
   - 不能为空
   - 不能重复（与数据库中已有数据对比）

2. **车辆状态验证**
   - 必须是有效的状态值：可用、维修中、报废
   - 自动转换中文状态名到对应的枚举值

3. **车辆型号验证**
   - 不能为空

4. **车辆使用单位验证**
   - 不能为空

### 错误信息处理
- 多个错误信息用分号（；）分隔
- 错误信息存储在 `errorsInfo` 字段中
- 前端可以根据该字段判断数据是否有效并显示具体错误原因

## 使用效果

### 前端展示
- 导入预览时，每行数据都会显示对应的验证结果
- 有错误的行会在"异常原因"列显示具体的错误信息
- 用户可以根据错误信息修正Excel文件后重新导入

### API响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": null,
      "vehicleNumber": "京A12345",
      "vehicleModel": "奔驰",
      "vehicleUseUnit": "测试单位",
      "tonnage": 5.0,
      "status": "AVAILABLE",
      "remark": "测试车辆",
      "statusName": "可用",
      "errorsInfo": null
    },
    {
      "id": null,
      "vehicleNumber": "",
      "vehicleModel": "",
      "vehicleUseUnit": "测试单位2",
      "tonnage": 3.0,
      "status": null,
      "remark": "有问题的车辆",
      "statusName": "无效状态",
      "errorsInfo": "车牌号不能为空；车辆型号不能为空；车辆状态不正确，应为：可用、维修中、报废"
    }
  ]
}
```

## 参考实现
本次修改完全参考了 `RemoveUserController` 中的 `importUserInfoPreview` 方法实现：
- 使用相同的ErrorVO继承模式
- 使用相同的错误信息字段命名和注解
- 使用相同的ActionResult返回方式
- 采用相同的数据验证和错误收集逻辑

这样确保了整个系统中导入预览功能的一致性和统一性。
