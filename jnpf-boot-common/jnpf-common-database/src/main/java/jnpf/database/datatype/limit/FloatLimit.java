package jnpf.database.datatype.limit;

import jnpf.database.datatype.db.interfaces.DtLimitBase;
import jnpf.database.datatype.limit.util.DtLimitUtil;
import jnpf.database.datatype.model.DtModel;
import jnpf.database.datatype.model.DtModelDTO;
import lombok.NoArgsConstructor;

/**
 * 浮点数据类型
 *
 * <AUTHOR>
 * @version V3.3
 * @copyright xx有限公司
 * @date 2022-06-06
 */
@NoArgsConstructor
public class FloatLimit extends DtLimitBase {

    public final static String CATEGORY = "type-Float";
    public final static String JAVA_TYPE = "float";

    public FloatLimit(Boolean modify){
        this.isModifyFlag = modify;
    }

    @Override
    public String initDtCategory() {
        return CATEGORY;
    }

    @Override
    public DtModel convert(DtModelDTO viewDtModel){
        DtModel dataTypeModel = DtLimitUtil.convertNumeric(viewDtModel);
        if(this.isModifyFlag){
            DtLimitUtil.getNumericLength(dataTypeModel);
        }
        return dataTypeModel;
    }

}
