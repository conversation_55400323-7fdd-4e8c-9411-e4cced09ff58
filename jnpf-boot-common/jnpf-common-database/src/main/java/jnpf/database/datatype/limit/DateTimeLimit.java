package jnpf.database.datatype.limit;

import jnpf.database.datatype.db.interfaces.DtLimitBase;
import jnpf.database.datatype.model.DtModel;
import jnpf.database.datatype.model.DtModelDTO;
import lombok.NoArgsConstructor;

/**
 * 时间数据类型
 *
 * <AUTHOR>
 * @version V3.3
 * @copyright xx有限公司
 * @date 2022-06-06
 */
@NoArgsConstructor
public class DateTimeLimit extends DtLimitBase {

    public final static String CATEGORY = "type-DateTime";
    public final static String JAVA_TYPE = "date";

    public DateTimeLimit(Boolean modify){
        this.isModifyFlag = modify;
    }

    @Override
    public String initDtCategory() {
        return CATEGORY;
    }

    @Override
    public DtModel convert(DtModelDTO dtModelDTO){
        DtModel dataTypeModel = new DtModel(dtModelDTO.getConvertTargetDtEnum());
        if(this.isModifyFlag){
            dataTypeModel.setFormatLengthStr("");
        }
        return dataTypeModel;
    }

}
