package jnpf.database.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import jnpf.util.DateUtil;
import jnpf.util.UserProvider;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V3.1.0
 * @copyright xx有限公司（https://www.xx.com）
 * @date 2020年12月22日 下午20:14
 */
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {


    @Override
    public void insertFill(MetaObject metaObject) {
        String userId = UserProvider.getLoginUserId();
        Object enabledMark = this.getFieldValByName("enabledMark", metaObject);
        Object creatorUserId = this.getFieldValByName("creatorUserId", metaObject);
        Object creatorTime = this.getFieldValByName("creatorTime", metaObject);
        Object creatorUser = this.getFieldValByName("creatorUser", metaObject);
        Object deleteMark = this.getFieldValByName("deleteMark", metaObject);
        if (enabledMark == null) {
            this.strictInsertFill(metaObject, "enabledMark", () -> 1, Integer.class);
        }
        if (creatorUserId == null) {
            this.strictInsertFill(metaObject, "creatorUserId", () -> userId, String.class);
        }
        if (creatorTime == null) {
            this.strictInsertFill(metaObject, "creatorTime", DateUtil::getNowDate, Date.class);
        }
        if (creatorUser == null) {
            this.strictInsertFill(metaObject, "creatorUser", () -> userId, String.class);
        }
        if (deleteMark == null) {
             this.strictInsertFill(metaObject, "deleteMark", Integer.class, 0);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        String userId = UserProvider.getLoginUserId();
        this.strictUpdateFill(metaObject, "lastModifyTime", DateUtil::getNowDate, Date.class);
        this.strictUpdateFill(metaObject, "lastModifyUserId", () -> userId, String.class);
        this.strictUpdateFill(metaObject, "lastModifyUser", () -> userId, String.class);
    }


}
