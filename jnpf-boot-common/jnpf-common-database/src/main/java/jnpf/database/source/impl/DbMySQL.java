package jnpf.database.source.impl;

import com.baomidou.mybatisplus.annotation.DbType;
import jnpf.database.constant.DbConst;
import jnpf.database.source.DbBase;
import jnpf.database.sql.model.DbStruct;

/**
 * MySQL模型
 *
 * <AUTHOR>
 * @version V3.2.0
 * @copyright xx有限公司
 * @date 2021/10/06
 */
public class DbMySQL extends DbBase {

    @Override
    protected void init(){
        setInstance(
                MYSQL,
                DbType.MYSQL,
                com.alibaba.druid.DbType.mysql,
                "3306",
                "root",
                "mysql",
                "com.mysql.cj.jdbc.Driver",
                "jdbc:mysql://{host}:{port}/{dbname}?useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&serverTimezone=GMT%2B8&useSSL=false"
                //connUrl = "jdbc:mysql://{host}:{port}/{dbname}?useUnicode=true&autoReconnect=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8";
                );
    }

    @Override
    public String getConnUrl(String prepareUrl, String host, Integer port, DbStruct struct) {
        prepareUrl = super.getConnUrl(prepareUrl, host, port, null);
        return prepareUrl.replace(DbConst.DB_NAME, struct.getMysqlDbName());
    }

}
