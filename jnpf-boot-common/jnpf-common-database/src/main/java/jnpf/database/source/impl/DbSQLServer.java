package jnpf.database.source.impl;

import com.baomidou.mybatisplus.annotation.DbType;
import jnpf.database.constant.DbConst;
import jnpf.database.source.DbBase;
import jnpf.database.sql.model.DbStruct;
import jnpf.util.data.DataSourceContextHolder;

/**
 * SQLServer模型
 *
 * <AUTHOR>
 * @version V3.2.0
 * @copyright xx有限公司
 * @date 2021/10/06
 */
public class DbSQLServer extends DbBase {

    /**
     * 驱动程序无法通过使用安全套接字层(SSL)加密与 SQL Server 建立安全连接。
     * 错误:“sun.security.validator.ValidatorException: PKIX path building failed
     *
     * 可以尝试连接：jdbc:sqlserver://{host}:{port};databaseName={dbname};encrypt=true;trustServerCertificate=true
     */
    @Override
    protected void init() {
        setInstance(
                SQL_SERVER,
                DbType.SQL_SERVER,
                com.alibaba.druid.DbType.sqlserver,
                "1433",
                "sa",
                "sqlserver",
                "com.microsoft.sqlserver.jdbc.SQLServerDriver",
                "jdbc:sqlserver://{host}:{port};databaseName={dbname};trustServerCertificate=true");
    }

    @Override
    public String getConnUrl(String prepareUrl, String host, Integer port, DbStruct struct) {
        prepareUrl = super.getConnUrl(prepareUrl, host, port, null);
        return prepareUrl.replace(DbConst.DB_NAME, struct.getSqlServerDbName()).replace(DbConst.DB_SCHEMA, struct.getSqlServerDbSchema());
    }

    @Override
    protected String getDynamicTableName(String tableName) {
        return DataSourceContextHolder.getDatasourceName()+".dbo." + tableName;
    }

}
