package jnpf.database.sql;

import jnpf.database.model.dbfield.JdbcColumnModel;
import jnpf.database.model.interfaces.DbSourceOrDbLink;
import jnpf.database.source.DbBase;
import jnpf.database.source.impl.DbMySQL;
import jnpf.database.util.DbTypeUtil;
import jnpf.exception.DataException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * SQL语句模板基类
 * 用以一些SQL语句不同库的特殊处理
 *
 * <AUTHOR>
 * @version V3.2.0
 * @copyright xx有限公司
 * @date 2021/10/6
 */
@Data
public abstract class SqlBase {

    /**
     * 数据基类
     */
    protected String dbEncode;




    protected DbBase getDb(){
        try {
            return DbTypeUtil.getEncodeDb(this.dbEncode);
        } catch (DataException e) {
            e.printStackTrace();
        }
        return new DbMySQL();
    }

    /**
     * 初始结构参数
     */
    public abstract void initStructParams(String table, DbSourceOrDbLink dbSourceOrDbLink);


    /**
     * 批量添加数据
     */
    // TODO 其余几个数据还没有添加方法
    public String batchInsertSql(List<List<JdbcColumnModel>> dataList, String table) {
        return "";
    }




}
