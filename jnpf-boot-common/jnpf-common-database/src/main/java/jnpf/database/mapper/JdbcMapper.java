package jnpf.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 类功能
 *
 * <AUTHOR>
 * @version v3.4.3
 * @copyrignt xx有限公司
 * @date 2022-10-18
 */
@Mapper
public interface JdbcMapper extends BaseMapper<Object> {

    List<Map<String, Object>> getList(@Param("sql") String sql);

}
