package jnpf.database.model.superQuery;

import jnpf.emnus.SearchMethodEnum;
import jnpf.model.visualJson.FieLdsModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 高级查询
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright xx有限公司（https://www.xx.com）
 * @date  2022/6/1
 */
@Data
public class SuperQueryJsonModel {
	private String logic = SearchMethodEnum.And.getSymbol();
	private List<FieLdsModel> groups = new ArrayList<>();
}
