package jnpf.database.model.dto;

import jnpf.database.source.DbBase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.ResultSet;

/**
 * 自定义模板参数对象
 *
 * <AUTHOR>
 * @version V3.2.0
 * @copyright xx有限公司
 * @date 2021/10/18
 */
@Data
public class ModelDTO {

    public ModelDTO(ResultSet resultSet, String dbEncode){
        this.resultSet = resultSet;
        this.dbEncode = dbEncode;
    }

    public ModelDTO(ResultSet resultSet, DbBase dbBase){
        this.resultSet = resultSet;
    }

    /**
     * 结果集
     */
    private ResultSet resultSet;

    /**
     * 数据基类
     */
    private String dbEncode;

}
