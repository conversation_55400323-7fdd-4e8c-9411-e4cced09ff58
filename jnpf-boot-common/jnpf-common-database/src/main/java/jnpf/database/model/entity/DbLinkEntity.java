package jnpf.database.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jnpf.database.model.dto.PrepSqlDTO;
import jnpf.database.util.DataSourceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据连接
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright xx有限公司
 * @date 2019年9月27日 上午9:18
 */
@Data
@TableName("base_db_link")
@NoArgsConstructor
public class DbLinkEntity extends DataSourceUtil {
    /**
     * 连接主键
     */
    @TableId("f_id")
    private String id;

    /**
     * 连接名称
     */
    @TableField("f_full_name")
    private String fullName;

    /**
     * Oracle扩展开关
     */
    @TableField("f_oracle_extend")
    private Integer oracleExtend;

    public static DbLinkEntity newInstance(String dbLinkId){
        return PrepSqlDTO.DB_LINK_FUN.apply(dbLinkId);
    }

    public DbLinkEntity(String dbType){
        super.setDbType(dbType);
    }

}
