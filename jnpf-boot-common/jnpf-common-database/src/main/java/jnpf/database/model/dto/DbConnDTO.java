package jnpf.database.model.dto;

import jnpf.database.model.interfaces.DbSourceOrDbLink;
import jnpf.database.source.DbBase;
import jnpf.database.util.DataSourceUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Connection;
import java.util.function.Function;

/**
 * 数据连接相关数据传输对象
 *
 * <AUTHOR>
 * @version V3.2.0
 * @copyright xx有限公司
 * @date 2021/10/12
 */
@Data
@NoArgsConstructor
public class DbConnDTO {

    public DbConnDTO(DbBase dbBase, DataSourceUtil dbSource, Connection conn){
        this.dbBase = dbBase;
        this.dbSourceInfo = dbSource;
        this.conn = conn;
    }

    /**
     * 数据库基类
     */
    private DbBase dbBase;

    /**
     * 数据源信息
     */
    private DbSourceOrDbLink dbSourceInfo;

    /**
     * 数据连接
     */
    private Connection conn;


    private Function<String, Connection> connFunc;

}
