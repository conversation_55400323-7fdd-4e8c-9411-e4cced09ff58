package jnpf.database.model;

import jnpf.database.enums.TenantDbSchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 *
 * BaseTenant模型
 * @版本： V3.1.0
 * @版权： xx有限公司（https://www.xx.com）
 * @作者： JNPF开发平台组
 * @日期： 2020-12-17 16:09:07
 */
@Data
@Accessors(chain = true)
public class TenantVO implements Serializable {

    private String dbName;
    /**
     * 数据源模式
     */
    private TenantDbSchema dataSchema;

    /**
     * 配置连接
     */
    private List<TenantLinkModel> linkList;

    public boolean isDefault(){
        return TenantDbSchema.DEFAULT.equals(dataSchema);
    }

}
