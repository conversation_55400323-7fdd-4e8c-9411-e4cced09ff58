package jnpf.database.model.superQuery;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 高级查询（代码生成器）
 *
 * <AUTHOR>
 * @version V3.4.2
 * @copyright xx有限公司（https://www.xx.com）
 * @date  2022/6/23
 */
@Data
@AllArgsConstructor
public class SuperQueryConditionModel<T> {
	private QueryWrapper<T> obj;
	private List<ConditionJsonModel> conditionList;
	private String matchLogic;
	private String tableName;
}
