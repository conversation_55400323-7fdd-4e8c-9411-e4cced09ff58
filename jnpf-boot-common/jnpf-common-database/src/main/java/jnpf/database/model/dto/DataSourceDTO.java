package jnpf.database.model.dto;//package jnpf.database.model.dto;
//
//import jnpf.database.sql.model.DbStruct;
//import jnpf.database.util.DataSourceUtil;
//import jnpf.util.StringUtil;
//import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
//
///**
// * 数据源参数传输对象
// * -- 注意：这里的参数dataSourceUtil是spring托管的全局唯一变量，此数据传输对象防止数据源互串
// *
// * <AUTHOR>
// * @version V3.2.0
// * @copyright xx有限公司
// * @date 2021/10/28
// */
//@Data
//public class DataSourceDTO extends DataSourceUtil{
//
//    /**
//     * 数据来源
//     * 0：自身创建  1：配置  2：数据连接
//     */
//    private Integer dataSourceFrom;
//
//    /**
//     * 表名
//     */
//    private String tableName;
//
//
//
//}
