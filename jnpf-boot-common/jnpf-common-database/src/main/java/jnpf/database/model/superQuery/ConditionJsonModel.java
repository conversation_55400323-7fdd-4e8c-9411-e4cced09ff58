package jnpf.database.model.superQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 高级查询
 *
 * <AUTHOR>
 * @version V3.4.2
 * @copyright xx有限公司（https://www.xx.com）
 * @date  2022/5/31
 */
@Data
public class ConditionJsonModel {
	private String field;
	private String fieldValue;
	private String symbol;
	private String tableName;
	private String jnpfKey;
	private String defaultValue;
	private String attr;
	/**
	 * 表单字段是否多选
	 */
	private boolean formMultiple;
}
