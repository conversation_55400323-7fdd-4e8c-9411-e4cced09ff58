package jnpf.permission.connector;

import java.util.Map;

/**
 * 拉取用户
 *
 * <AUTHOR>
 * @version: V3.1.0
 * @copyright xx有限公司
 * @date ：2022/7/28 14:27
 */
public interface UserInfoService {

    /**
     * 添加
     *
     * @param map
     */
    Boolean create(Map<String, Object> map);

    /**
     * 修改
     *
     * @param map
     */
    Boolean update(Map<String, Object> map);

    /**
     * 删除
     *
     * @param map
     */
    Boolean delete(Map<String, Object> map);

    /**
     * 获取信息
     *
     * @param id
     */
    Map<String, Object> getInfo(String id);

}
